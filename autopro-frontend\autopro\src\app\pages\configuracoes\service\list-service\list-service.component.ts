import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { CadServiceComponent } from '../cad-service/cad-service.component';
import { GlobalService } from '../../../../../services/global.service';
import { ServiceModel } from '../model/service.model';

@Component({
  selector: 'app-list-service',
  imports: [
    TituloComponent,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    CommonModule,
    NzIconModule,
    CadServiceComponent,
    NzPopconfirmModule,
    NzToolTipModule
  ],
  providers: [
    NzMessageService,
    NzNotificationService
  ],
  templateUrl: './list-service.component.html',
  styleUrl: './list-service.component.scss',
  host: { ngSkipHydration: 'true' }
})
export class ListServiceComponent {

  tituloPagina: string = "Lista de Serviços";
  firstLevel:string = "Configurações";
  secondLevel:string = "Serviços";
  subtitle: string = "Listagem de Serviços";
  isLoadingNew:boolean = false;
  isLoadingTable:boolean = false;
  lstService: ServiceModel[] = [];
  showModalNovo: boolean = false; 
  serviceModel : ServiceModel | null = null;


constructor(
  private messageService: NzMessageService,
  private notificationService: NzNotificationService,
  private globalService: GlobalService
) {

}

  ngOnInit() {
    this.loadServices();
  }

  onModalClosed() {
    this.showModalNovo = false;
    this.loadServices();
  }

  onEdit(item: ServiceModel) {
    this.showModalNovo = true;
    console.log(item);
    this.serviceModel = item;
  }

  onDelete(id: number) {
    this.globalService.delete(`service/delete/${id}`).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Serviço excluído com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.loadServices();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao excluir serviço!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  onNew() {
    console.log('onNew');
    this.showModalNovo = true;
    this.serviceModel = null;
    console.log(this.showModalNovo)
  }

  cancel(): void {
    this.messageService.info('click cancel');
    this.notificationService.error(
      'Exclusão',
      'Registro excluído com sucesso!',
      {
        nzDuration: 3000,
        nzClass: {
          'ant-notification-notice': 'ant-notification-notice.error'
        }
      }
    );
  }

  confirm(): void {
    this.messageService.info('click confirm');
  }

  loadServices() {
    this.isLoadingNew = true;
    this.isLoadingTable = true;
    this.globalService.get('service/list').subscribe({
        next: (response: any)  =>  {
          this.lstService = response;
            this.isLoadingNew = false;
            this.isLoadingTable = false;
        },
      error: (error: any) => {
        this.isLoadingNew = false;
        this.notificationService.error(
          'Erro',
          'Erro ao carregar serviços!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      },
      complete: () => {
        this.isLoadingNew = false;
      }
    });
  }

  isImageFile(icon: string): boolean {
    if (!icon) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.svg'];
    return imageExtensions.some(ext => icon.toLowerCase().endsWith(ext));
  }

  getImagePath(filename: string): string {
    // Retorna o caminho da imagem no servidor backend
    return `http://localhost:5090/uploads/service-icons/${filename}`;
  }

}
