{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "AssetFile": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "183882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:02:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="}]}, {"Route": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.vpvb6w4lrr.png", "AssetFile": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "183882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:02:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vpvb6w4lrr"}, {"Name": "integrity", "Value": "sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="}, {"Name": "label", "Value": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png"}]}]}