<nz-modal
    ngSkipHydration="true"
      nzDraggable
      nzCentered
      [(nzVisible)]="isVisible"
      [nzTitle]="titlePage"
      (nzOnCancel)="handleCancel()"
      nzWidth="820px"
    >
      <ng-container *nzModalContent>
        <form nz-form [formGroup]="form" skipHydration="true">
            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="name" nzRequired>Nome do Serviço</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Nome do serviço é obrigatório">
                <input type="hidden" formControlName="id" id="id" />
                <input nz-input formControlName="name" id="name" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="icon" nzRequired>Ícone</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Ícone é obrigatório">
                <div class="upload-section">
                  <div class="upload-item">
                    <div class="upload-input-container">
                      <input type="text"
                             class="upload-input"
                             [value]="getIconFileName()"
                             placeholder="Selecione uma imagem para o ícone"
                             readonly>
                      <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedIcon"></i>
                    </div>
                    <button type="button" class="upload-btn" (click)="triggerIconUpload()">
                      UPLOAD
                    </button>
                    <input type="file" #iconFile (change)="onIconSelected($event)" style="display: none;" accept=".jpg,.jpeg,.png,.svg">
                  </div>
                  <div class="icon-preview" *ngIf="iconPreviewUrl">
                    <img [src]="iconPreviewUrl" alt="Preview do ícone" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; margin-top: 8px;">
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="description" nzRequired>Descrição</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Descrição é obrigatória">
                <textarea nz-input formControlName="description" id="description" rows="3"></textarea>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="valueAmount" nzRequired>Valor (R$)</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Valor é obrigatório">
                <nz-input-number 
                  formControlName="valueAmount" 
                  id="valueAmount"
                  [nzMin]="0"
                  [nzStep]="0.01"
                  [nzPrecision]="2"
                  nzPlaceHolder="0.00"
                  style="width: 100%">
                </nz-input-number>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="active">Ativo</nz-form-label>
              <nz-form-control [nzSpan]="14">
                <nz-switch formControlName="active" id="active"></nz-switch>
              </nz-form-control>
            </nz-form-item>
        </form>
      </ng-container>
      <div *nzModalFooter>
        <button nz-button nzType="default" (click)="handleCancel()">Cancelar</button>
        <button nz-button nzType="primary" (click)="save()">Salvar</button>
      </div>
    </nz-modal>
