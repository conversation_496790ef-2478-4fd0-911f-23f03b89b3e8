﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/AutoVPro/uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\service-icons\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"183882"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Jul 2025 22:02:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/AutoVPro/uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.vpvb6w4lrr.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\service-icons\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vpvb6w4lrr"},{"Name":"integrity","Value":"sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="},{"Name":"label","Value":"_content/AutoVPro/uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"183882"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Jul 2025 22:02:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>