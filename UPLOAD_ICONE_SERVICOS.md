# Implementação de Upload de Ícones para Serviços

## Resumo das Alterações

Foi implementada a funcionalidade de upload de imagens para ícones de serviços, substituindo o campo de texto que anteriormente recebia classes de ícones FontAwesome.

## Alterações no Frontend

### 1. Componente de Cadastro de Serviços (`cad-service.component.html`)
- Substituído o campo de input de texto por um componente de upload de imagem
- Adicionado preview da imagem selecionada
- Implementado validação visual com ícone de sucesso

### 2. Componente de Cadastro de Serviços (`cad-service.component.ts`)
- Adicionadas propriedades para controle do upload: `uploadedIcon`, `iconPreviewUrl`
- Implementados métodos:
  - `triggerIconUpload()`: Abre o seletor de arquivos
  - `onIconSelected()`: Valida e processa o arquivo selecionado
  - `uploadIconToServer()`: Envia o arquivo para o backend
  - `getIconFileName()`: Retorna o nome do arquivo selecionado
  - `resetIconUpload()`: Limpa o upload

### 3. Estilos CSS (`cad-service.component.scss`)
- Adicionados estilos para o componente de upload
- Estilização do botão de upload e preview da imagem

### 4. Listagem de Serviços (`list-service.component.html`)
- Atualizada para exibir imagens quando o ícone for um arquivo de imagem
- Mantida compatibilidade com ícones FontAwesome existentes

### 5. Listagem de Serviços (`list-service.component.ts`)
- Adicionados métodos:
  - `isImageFile()`: Verifica se o ícone é um arquivo de imagem
  - `getImagePath()`: Retorna o caminho completo da imagem no servidor

### 6. GlobalService (`global.service.ts`)
- Adicionado método `postFile()` para upload de arquivos
- Remove o header Content-Type para permitir multipart/form-data

## Alterações no Backend

### 1. UploadController (`UploadController.cs`)
- Novo controller para gerenciar uploads de arquivos
- Endpoint `POST /api/upload/service-icon` para upload de ícones
- Endpoint `DELETE /api/upload/service-icon/{fileName}` para deletar ícones
- Validações implementadas:
  - Tipos de arquivo permitidos: JPG, PNG, SVG
  - Tamanho máximo: 2MB
  - Geração de nomes únicos para evitar conflitos

### 2. Program.cs
- Adicionada configuração `app.UseStaticFiles()` para servir arquivos estáticos

### 3. Estrutura de Pastas
- Criada pasta `wwwroot/uploads/service-icons/` para armazenar as imagens

## Funcionalidades Implementadas

### ✅ Upload de Imagem
- Seleção de arquivo através de interface amigável
- Validação de tipo de arquivo (JPG, PNG, SVG)
- Validação de tamanho (máximo 2MB)
- Preview da imagem selecionada
- Upload automático para o servidor

### ✅ Exibição na Listagem
- Detecção automática se o ícone é imagem ou FontAwesome
- Exibição de imagens redimensionadas (32x32px)
- Fallback para ícones FontAwesome existentes

### ✅ Compatibilidade
- Mantém compatibilidade com ícones FontAwesome existentes
- Não quebra dados já cadastrados no sistema

## Como Usar

1. **Cadastrar Novo Serviço:**
   - Clique no botão "UPLOAD" no campo Ícone
   - Selecione uma imagem (JPG, PNG ou SVG)
   - A imagem será carregada automaticamente
   - O preview será exibido abaixo do campo

2. **Visualizar na Listagem:**
   - Serviços com imagens mostrarão a imagem redimensionada
   - Serviços com ícones FontAwesome continuarão mostrando o ícone

## Próximos Passos Sugeridos

### 1. Melhorias de UX
- [ ] Adicionar indicador de progresso durante upload
- [ ] Permitir arrastar e soltar arquivos
- [ ] Adicionar opção de recortar/redimensionar imagem

### 2. Gerenciamento de Arquivos
- [ ] Implementar limpeza automática de arquivos órfãos
- [ ] Adicionar compressão automática de imagens
- [ ] Implementar versionamento de arquivos

### 3. Segurança
- [ ] Adicionar autenticação para endpoints de upload
- [ ] Implementar verificação de vírus em arquivos
- [ ] Adicionar rate limiting para uploads

### 4. Performance
- [ ] Implementar cache para imagens
- [ ] Adicionar CDN para servir arquivos estáticos
- [ ] Otimizar tamanho das imagens automaticamente

## Estrutura de Arquivos Criados/Modificados

```
Frontend:
├── src/app/pages/configuracoes/service/cad-service/
│   ├── cad-service.component.html (modificado)
│   ├── cad-service.component.ts (modificado)
│   └── cad-service.component.scss (modificado)
├── src/app/pages/configuracoes/service/list-service/
│   ├── list-service.component.html (modificado)
│   └── list-service.component.ts (modificado)
├── src/services/global.service.ts (modificado)
└── src/assets/images/service-icons/ (criado)

Backend:
├── Web/UploadController.cs (criado)
├── Program.cs (modificado)
└── wwwroot/uploads/service-icons/ (criado)
```

## Observações Técnicas

- O campo `icon` no banco de dados continua sendo varchar(255)
- Para imagens, armazena o nome do arquivo gerado pelo servidor
- Para ícones FontAwesome, continua armazenando a classe CSS
- A detecção é feita pela extensão do arquivo (.jpg, .png, .svg)
