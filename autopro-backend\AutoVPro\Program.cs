using System.Reflection;
using System.Text;
using AutoVPro.Business;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database;
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

var jwtKey = builder.Configuration["Jwt:Key"] ;
var jwtIssuer = builder.Configuration["Jwt:Issuer"];

builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtIssuer,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey))
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("HasDepartment", policy =>
        policy.RequireClaim("department", "IT", "HR"));
});


builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configuração CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.SetIsOriginAllowed(origin =>
                origin.StartsWith("http://localhost:") ||
                origin.StartsWith("https://localhost:"))
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});
builder.Services.AddDbContext<AutoProDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DatabaseConnection")));

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "AutoVPro",
        Version = "v1"
    });

    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFilename);
    c.IncludeXmlComments(xmlPath);
});

//DEPENDENCY INJECTION
builder.Services.AddScoped<IBusiness<UserGroupDTO>, UserGroupBusiness>();
builder.Services.AddScoped<IDatabase<UserGroupModel>, UserGroupDb>();

builder.Services.AddScoped<ILoginBusiness, LoginBusiness>();

builder.Services.AddScoped<IBusiness<UserDTO>, UserBusiness>();
builder.Services.AddScoped<IDatabase<UserModel>, UserDb>();

builder.Services.AddScoped<IBusiness<ServiceDTO>, ServiceBusiness>();
builder.Services.AddScoped<IDatabase<ServiceModel>, ServiceDb>();

builder.Services.AddAutoMapper(typeof(Program).Assembly);

var app = builder.Build();

// Swagger middleware
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "AutoVPro v1");
});

//app.UseHttpsRedirection();

// Configurar arquivos estáticos
app.UseStaticFiles();

// Usar CORS
app.UseCors("AllowFrontend");

app.MapControllers();

app.Run();

