{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-input-number.mjs"], "sourcesContent": ["import { FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ENTER, DOWN_ARROW, UP_ARROW } from '@angular/cdk/keycodes';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { input, numberAttribute, booleanAttribute, output, inject, viewChild, ElementRef, Injector, signal, linkedSignal, contentChild, computed, DestroyRef, afterNextRender, untracked, forwardRef, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { NzFormStatusService, NzFormItemFeedbackIconComponent } from 'ng-zorro-antd/core/form';\nimport { isNotNil, getStatusClassNames, isNil } from 'ng-zorro-antd/core/util';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzInputPrefixDirective, NzInputSuffixDirective, NzInputAddonBeforeDirective, NzInputAddonAfterDirective } from 'ng-zorro-antd/input';\nimport * as i1 from 'ng-zorro-antd/space';\nimport { NZ_SPACE_COMPACT_SIZE, NZ_SPACE_COMPACT_ITEM_TYPE, NzSpaceCompactItemDirective } from 'ng-zorro-antd/space';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"input\"];\nconst _c1 = [\"inputNumberHost\"];\nconst _c2 = [[[\"\", \"nzInputAddonBefore\", \"\"]], [[\"\", \"nzInputAddonAfter\", \"\"]], [[\"\", \"nzInputPrefix\", \"\"]], [[\"\", \"nzInputSuffix\", \"\"]], [[\"\", \"nzInputNumberUpIcon\", \"\"]], [[\"\", \"nzInputNumberDownIcon\", \"\"]]];\nconst _c3 = [\"[nzInputAddonBefore]\", \"[nzInputAddonAfter]\", \"[nzInputPrefix]\", \"[nzInputSuffix]\", \"[nzInputNumberUpIcon]\", \"[nzInputNumberDownIcon]\"];\nfunction NzInputNumberComponent_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzInputNumberComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const inputNumberWithAddonInner_r1 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberWithAddonInner_r1);\n  }\n}\nfunction NzInputNumberComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzInputNumberComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const inputNumberWithAffixInner_r2 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberWithAffixInner_r2);\n  }\n}\nfunction NzInputNumberComponent_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzInputNumberComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const inputNumberInner_r3 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberInner_r3);\n  }\n}\nfunction NzInputNumberComponent_ng_template_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzInputNumberComponent_ng_template_3_Conditional_2_ng_template_0_Template(rf, ctx) {}\nfunction NzInputNumberComponent_ng_template_3_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_ng_template_3_Conditional_2_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const inputNumberWithAffix_r4 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberWithAffix_r4);\n  }\n}\nfunction NzInputNumberComponent_ng_template_3_Conditional_3_ng_template_0_Template(rf, ctx) {}\nfunction NzInputNumberComponent_ng_template_3_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_ng_template_3_Conditional_3_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const inputNumber_r5 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumber_r5);\n  }\n}\nfunction NzInputNumberComponent_ng_template_3_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzInputNumberComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, NzInputNumberComponent_ng_template_3_Conditional_1_Template, 2, 0, \"div\", 10)(2, NzInputNumberComponent_ng_template_3_Conditional_2_Template, 1, 1, null, 8)(3, NzInputNumberComponent_ng_template_3_Conditional_3_Template, 1, 1, null, 8)(4, NzInputNumberComponent_ng_template_3_Conditional_4_Template, 2, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r5.addonBefore() ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r5.hasAffix() ? 2 : 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r5.addonAfter() ? 4 : -1);\n  }\n}\nfunction NzInputNumberComponent_ng_template_5_ng_template_1_Template(rf, ctx) {}\nfunction NzInputNumberComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NzInputNumberComponent_ng_template_5_ng_template_1_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    const inputNumberWithAffixInner_r2 = i0.ɵɵreference(8);\n    i0.ɵɵclassMap(ctx_r5.affixWrapperClass());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberWithAffixInner_r2);\n  }\n}\nfunction NzInputNumberComponent_ng_template_7_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzInputNumberComponent_ng_template_7_ng_template_1_Template(rf, ctx) {}\nfunction NzInputNumberComponent_ng_template_7_Conditional_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-form-item-feedback-icon\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"status\", ctx_r5.finalStatus());\n  }\n}\nfunction NzInputNumberComponent_ng_template_7_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵprojection(1, 3);\n    i0.ɵɵtemplate(2, NzInputNumberComponent_ng_template_7_Conditional_2_Conditional_2_Template, 1, 1, \"nz-form-item-feedback-icon\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r5.hasFeedback() && ctx_r5.finalStatus() ? 2 : -1);\n  }\n}\nfunction NzInputNumberComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputNumberComponent_ng_template_7_Conditional_0_Template, 2, 0, \"span\", 11)(1, NzInputNumberComponent_ng_template_7_ng_template_1_Template, 0, 0, \"ng-template\", 8)(2, NzInputNumberComponent_ng_template_7_Conditional_2_Template, 3, 1, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    const inputNumber_r5 = i0.ɵɵreference(10);\n    i0.ɵɵconditional(ctx_r5.prefix() ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumber_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r5.suffix() || ctx_r5.hasFeedback() ? 2 : -1);\n  }\n}\nfunction NzInputNumberComponent_ng_template_9_ng_template_2_Template(rf, ctx) {}\nfunction NzInputNumberComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", null, 5);\n    i0.ɵɵtemplate(2, NzInputNumberComponent_ng_template_9_ng_template_2_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    const inputNumberInner_r3 = i0.ɵɵreference(12);\n    i0.ɵɵclassMap(ctx_r5.inputNumberClass());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputNumberInner_r3);\n  }\n}\nfunction NzInputNumberComponent_ng_template_11_Conditional_0_ProjectionFallback_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 20);\n  }\n}\nfunction NzInputNumberComponent_ng_template_11_Conditional_0_ProjectionFallback_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-icon\", 21);\n  }\n}\nfunction NzInputNumberComponent_ng_template_11_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17, 7);\n    i0.ɵɵlistener(\"mouseup\", function NzInputNumberComponent_ng_template_11_Conditional_0_Template_div_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.stopAutoStep());\n    })(\"mouseleave\", function NzInputNumberComponent_ng_template_11_Conditional_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.stopAutoStep());\n    });\n    i0.ɵɵelementStart(2, \"span\", 18);\n    i0.ɵɵlistener(\"mousedown\", function NzInputNumberComponent_ng_template_11_Conditional_0_Template_span_mousedown_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onStepMouseDown($event, true));\n    });\n    i0.ɵɵprojection(3, 4, null, NzInputNumberComponent_ng_template_11_Conditional_0_ProjectionFallback_3_Template, 1, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 19);\n    i0.ɵɵlistener(\"mousedown\", function NzInputNumberComponent_ng_template_11_Conditional_0_Template_span_mousedown_5_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onStepMouseDown($event, false));\n    });\n    i0.ɵɵprojection(6, 5, null, NzInputNumberComponent_ng_template_11_Conditional_0_ProjectionFallback_6_Template, 1, 0);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"ant-input-number-handler-up-disabled\", ctx_r5.upDisabled());\n    i0.ɵɵattribute(\"aria-disabled\", ctx_r5.upDisabled());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"ant-input-number-handler-down-disabled\", ctx_r5.downDisabled());\n    i0.ɵɵattribute(\"aria-disabled\", ctx_r5.downDisabled());\n  }\n}\nfunction NzInputNumberComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, NzInputNumberComponent_ng_template_11_Conditional_0_Template, 8, 6, \"div\", 14);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"input\", 16, 6);\n    i0.ɵɵlistener(\"input\", function NzInputNumberComponent_ng_template_11_Template_input_input_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const input_r9 = i0.ɵɵreference(3);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onInput(input_r9.value));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_9_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r5.nzControls() ? 0 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r5.displayValue())(\"placeholder\", (tmp_9_0 = ctx_r5.nzPlaceHolder()) !== null && tmp_9_0 !== undefined ? tmp_9_0 : \"\")(\"disabled\", ctx_r5.finalDisabled())(\"readOnly\", ctx_r5.nzReadOnly());\n    i0.ɵɵattribute(\"aria-valuemin\", ctx_r5.nzMin())(\"aria-valuemax\", ctx_r5.nzMax())(\"id\", ctx_r5.nzId())(\"step\", ctx_r5.nzStep())(\"value\", ctx_r5.displayValue());\n  }\n}\nclass NzInputNumberComponent {\n  nzId = input(null);\n  nzSize = input('default');\n  nzPlaceHolder = input(null);\n  nzStatus = input('');\n  nzStep = input(1, {\n    transform: numberAttribute\n  });\n  nzMin = input(Number.MIN_SAFE_INTEGER, {\n    transform: numberAttribute\n  });\n  nzMax = input(Number.MAX_SAFE_INTEGER, {\n    transform: numberAttribute\n  });\n  nzPrecision = input(null);\n  nzParser = input();\n  nzFormatter = input();\n  nzDisabled = input(false, {\n    transform: booleanAttribute\n  });\n  nzReadOnly = input(false, {\n    transform: booleanAttribute\n  });\n  nzAutoFocus = input(false, {\n    transform: booleanAttribute\n  });\n  nzBordered = input(true, {\n    transform: booleanAttribute\n  });\n  nzKeyboard = input(true, {\n    transform: booleanAttribute\n  });\n  nzControls = input(true, {\n    transform: booleanAttribute\n  });\n  nzBlur = output();\n  nzFocus = output();\n  nzOnStep = output();\n  onChange = () => {};\n  onTouched = () => {};\n  isDisabledFirstChange = true;\n  compactSize = inject(NZ_SPACE_COMPACT_SIZE, {\n    optional: true\n  });\n  inputRef = viewChild.required('input');\n  hostRef = viewChild('inputNumberHost');\n  elementRef = inject(ElementRef);\n  injector = inject(Injector);\n  focusMonitor = inject(FocusMonitor);\n  directionality = inject(Directionality);\n  nzFormStatusService = inject(NzFormStatusService, {\n    optional: true\n  });\n  autoStepTimer = null;\n  defaultFormater = value => {\n    const precision = this.nzPrecision();\n    if (isNotNil(precision)) {\n      return value.toFixed(precision);\n    }\n    return value.toString();\n  };\n  value = signal(null);\n  displayValue = signal('');\n  dir = toSignal(this.directionality.change, {\n    initialValue: this.directionality.value\n  });\n  focused = signal(false);\n  hasFeedback = signal(false);\n  finalStatus = linkedSignal(() => this.nzStatus());\n  finalDisabled = linkedSignal(() => this.nzDisabled());\n  prefix = contentChild(NzInputPrefixDirective);\n  suffix = contentChild(NzInputSuffixDirective);\n  addonBefore = contentChild(NzInputAddonBeforeDirective);\n  addonAfter = contentChild(NzInputAddonAfterDirective);\n  hasAffix = computed(() => !!this.prefix() || !!this.suffix() || this.hasFeedback());\n  hasAddon = computed(() => !!this.addonBefore() || !!this.addonAfter());\n  class = computed(() => {\n    if (this.hasAddon()) {\n      return this.groupWrapperClass();\n    }\n    if (this.hasAffix()) {\n      return this.affixWrapperClass();\n    }\n    return this.inputNumberClass();\n  });\n  inputNumberClass = computed(() => {\n    return {\n      'ant-input-number': true,\n      'ant-input-number-lg': this.finalSize() === 'large',\n      'ant-input-number-sm': this.finalSize() === 'small',\n      'ant-input-number-disabled': this.finalDisabled(),\n      'ant-input-number-readonly': this.nzReadOnly(),\n      'ant-input-number-borderless': !this.nzBordered(),\n      'ant-input-number-focused': this.focused(),\n      'ant-input-number-rtl': this.dir() === 'rtl',\n      'ant-input-number-in-form-item': !!this.nzFormStatusService,\n      'ant-input-number-out-of-range': this.value() !== null && !isInRange(this.value(), this.nzMin(), this.nzMax()),\n      ...getStatusClassNames('ant-input-number', this.finalStatus(), this.hasFeedback())\n    };\n  });\n  affixWrapperClass = computed(() => {\n    return {\n      'ant-input-number-affix-wrapper': true,\n      'ant-input-number-affix-wrapper-disabled': this.finalDisabled(),\n      'ant-input-number-affix-wrapper-readonly': this.nzReadOnly(),\n      'ant-input-number-affix-wrapper-borderless': !this.nzBordered(),\n      'ant-input-number-affix-wrapper-focused': this.focused(),\n      'ant-input-number-affix-wrapper-rtl': this.dir() === 'rtl',\n      ...getStatusClassNames('ant-input-number-affix-wrapper', this.finalStatus(), this.hasFeedback())\n    };\n  });\n  groupWrapperClass = computed(() => {\n    return {\n      'ant-input-number-group-wrapper': true,\n      'ant-input-number-group-wrapper-rtl': this.dir() === 'rtl',\n      ...getStatusClassNames('ant-input-number-group-wrapper', this.finalStatus(), this.hasFeedback())\n    };\n  });\n  finalSize = computed(() => {\n    if (this.compactSize) {\n      return this.compactSize();\n    }\n    return this.nzSize();\n  });\n  upDisabled = computed(() => {\n    return !isNil(this.value()) && this.value() >= this.nzMax();\n  });\n  downDisabled = computed(() => {\n    return !isNil(this.value()) && this.value() <= this.nzMin();\n  });\n  constructor() {\n    const destroyRef = inject(DestroyRef);\n    afterNextRender(() => {\n      const hostRef = this.hostRef();\n      const element = hostRef ? hostRef : this.elementRef;\n      this.focusMonitor.monitor(element, true).pipe(takeUntilDestroyed(destroyRef)).subscribe(origin => {\n        this.focused.set(!!origin);\n        if (origin) {\n          this.nzFocus.emit();\n        } else {\n          this.fixValue();\n          this.onTouched();\n          this.nzBlur.emit();\n        }\n      });\n      destroyRef.onDestroy(() => {\n        this.focusMonitor.stopMonitoring(element);\n      });\n    });\n    this.nzFormStatusService?.formStatusChanges.pipe(takeUntilDestroyed()).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.finalStatus.set(status);\n      this.hasFeedback.set(hasFeedback);\n    });\n  }\n  ngOnInit() {\n    if (this.nzAutoFocus()) {\n      afterNextRender(() => this.focus(), {\n        injector: this.injector\n      });\n    }\n  }\n  writeValue(value) {\n    untracked(() => {\n      this.value.set(value);\n      this.setValue(value);\n    });\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    untracked(() => {\n      this.finalDisabled.set(this.isDisabledFirstChange && this.nzDisabled() || disabled);\n    });\n    this.isDisabledFirstChange = false;\n  }\n  focus() {\n    this.inputRef().nativeElement.focus();\n  }\n  blur() {\n    this.inputRef().nativeElement.blur();\n  }\n  step(event, up) {\n    // Ignore step since out of range\n    if (up && this.upDisabled() || !up && this.downDisabled()) {\n      return;\n    }\n    // When hold the shift key, the step is 10 times\n    let step = event.shiftKey ? this.nzStep() * 10 : this.nzStep();\n    if (!up) {\n      step = -step;\n    }\n    const places = getDecimalPlaces(step);\n    const multiple = 10 ** places;\n    const nextValue = getRangeValue(\n    // Convert floating point numbers to integers to avoid floating point math errors\n    (Math.round((this.value() || 0) * multiple) + Math.round(step * multiple)) / multiple, this.nzMin(), this.nzMax(), this.nzPrecision());\n    this.setValue(nextValue);\n    this.nzOnStep.emit({\n      type: up ? 'up' : 'down',\n      value: this.value(),\n      offset: this.nzStep()\n    });\n    this.focus();\n  }\n  setValue(value) {\n    const formatter = this.nzFormatter() ?? this.defaultFormater;\n    const precision = this.nzPrecision();\n    if (isNotNil(precision)) {\n      value &&= +value.toFixed(precision);\n    }\n    const formatedValue = value === null ? '' : formatter(value);\n    this.displayValue.set(formatedValue);\n    this.updateValue(value);\n  }\n  setValueByTyping(value) {\n    if (value === '') {\n      this.displayValue.set('');\n      this.updateValue(null);\n      return;\n    }\n    const parser = this.nzParser() ?? defaultParser;\n    const parsedValue = parser(value);\n    if (isNotCompleteNumber(value) || Number.isNaN(parsedValue)) {\n      this.displayValue.set(value);\n      return;\n    }\n    const formattedValue = this.nzFormatter()?.(parsedValue) ?? parsedValue.toString();\n    this.displayValue.set(formattedValue);\n    if (!isInRange(parsedValue, this.nzMin(), this.nzMax())) {\n      return;\n    }\n    this.updateValue(parsedValue);\n  }\n  updateValue(value) {\n    if (this.value() !== value) {\n      this.value.set(value);\n      this.onChange(value);\n    }\n  }\n  fixValue() {\n    const displayValue = this.displayValue();\n    if (displayValue === '') {\n      return;\n    }\n    const parser = this.nzParser() ?? defaultParser;\n    let fixedValue = parser(displayValue);\n    // If parsing fails, revert to the previous value\n    if (Number.isNaN(fixedValue)) {\n      fixedValue = this.value();\n    } else {\n      const precision = this.nzPrecision();\n      // fix precision\n      if (isNotNil(precision) && getDecimalPlaces(fixedValue) !== precision) {\n        fixedValue = +fixedValue.toFixed(precision);\n      }\n      // fix range\n      if (!isInRange(fixedValue, this.nzMin(), this.nzMax())) {\n        fixedValue = getRangeValue(fixedValue, this.nzMin(), this.nzMax(), precision);\n      }\n    }\n    this.setValue(fixedValue);\n  }\n  stopAutoStep() {\n    if (this.autoStepTimer !== null) {\n      clearTimeout(this.autoStepTimer);\n      this.autoStepTimer = null;\n    }\n  }\n  onStepMouseDown(event, up) {\n    event.preventDefault();\n    this.stopAutoStep();\n    this.step(event, up);\n    // Loop step for interval\n    const loopStep = () => {\n      this.step(event, up);\n      this.autoStepTimer = setTimeout(loopStep, STEP_INTERVAL);\n    };\n    // First time press will wait some time to trigger loop step update\n    this.autoStepTimer = setTimeout(loopStep, STEP_DELAY);\n  }\n  onKeyDown(event) {\n    switch (event.keyCode) {\n      case UP_ARROW:\n        event.preventDefault();\n        this.nzKeyboard() && this.step(event, true);\n        break;\n      case DOWN_ARROW:\n        event.preventDefault();\n        this.nzKeyboard() && this.step(event, false);\n        break;\n      case ENTER:\n        this.fixValue();\n        break;\n    }\n  }\n  onInput(value) {\n    this.setValueByTyping(value);\n  }\n  static ɵfac = function NzInputNumberComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputNumberComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzInputNumberComponent,\n    selectors: [[\"nz-input-number\"]],\n    contentQueries: function NzInputNumberComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.prefix, NzInputPrefixDirective, 5);\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.suffix, NzInputSuffixDirective, 5);\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.addonBefore, NzInputAddonBeforeDirective, 5);\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx.addonAfter, NzInputAddonAfterDirective, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance(4);\n      }\n    },\n    viewQuery: function NzInputNumberComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuerySignal(ctx.inputRef, _c0, 5);\n        i0.ɵɵviewQuerySignal(ctx.hostRef, _c1, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance(2);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function NzInputNumberComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function NzInputNumberComponent_keydown_HostBindingHandler($event) {\n          return ctx.onKeyDown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.class());\n      }\n    },\n    inputs: {\n      nzId: [1, \"nzId\"],\n      nzSize: [1, \"nzSize\"],\n      nzPlaceHolder: [1, \"nzPlaceHolder\"],\n      nzStatus: [1, \"nzStatus\"],\n      nzStep: [1, \"nzStep\"],\n      nzMin: [1, \"nzMin\"],\n      nzMax: [1, \"nzMax\"],\n      nzPrecision: [1, \"nzPrecision\"],\n      nzParser: [1, \"nzParser\"],\n      nzFormatter: [1, \"nzFormatter\"],\n      nzDisabled: [1, \"nzDisabled\"],\n      nzReadOnly: [1, \"nzReadOnly\"],\n      nzAutoFocus: [1, \"nzAutoFocus\"],\n      nzBordered: [1, \"nzBordered\"],\n      nzKeyboard: [1, \"nzKeyboard\"],\n      nzControls: [1, \"nzControls\"]\n    },\n    outputs: {\n      nzBlur: \"nzBlur\",\n      nzFocus: \"nzFocus\",\n      nzOnStep: \"nzOnStep\"\n    },\n    exportAs: [\"nzInputNumber\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => NzInputNumberComponent),\n      multi: true\n    }, {\n      provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n      useValue: 'input-number'\n    }]), i0.ɵɵHostDirectivesFeature([i1.NzSpaceCompactItemDirective])],\n    ngContentSelectors: _c3,\n    decls: 13,\n    vars: 1,\n    consts: [[\"inputNumberWithAddonInner\", \"\"], [\"inputNumberWithAffix\", \"\"], [\"inputNumberWithAffixInner\", \"\"], [\"inputNumber\", \"\"], [\"inputNumberInner\", \"\"], [\"inputNumberHost\", \"\"], [\"input\", \"\"], [\"handlers\", \"\"], [3, \"ngTemplateOutlet\"], [1, \"ant-input-number-wrapper\", \"ant-input-number-group\"], [1, \"ant-input-number-group-addon\"], [1, \"ant-input-number-prefix\"], [1, \"ant-input-number-suffix\"], [3, \"status\"], [1, \"ant-input-number-handler-wrap\"], [1, \"ant-input-number-input-wrap\"], [\"autocomplete\", \"off\", \"role\", \"spinbutton\", 1, \"ant-input-number-input\", 3, \"input\", \"value\", \"placeholder\", \"disabled\", \"readOnly\"], [1, \"ant-input-number-handler-wrap\", 3, \"mouseup\", \"mouseleave\"], [\"role\", \"button\", \"unselectable\", \"on\", 1, \"ant-input-number-handler\", \"ant-input-number-handler-up\", 3, \"mousedown\"], [\"role\", \"button\", \"unselectable\", \"on\", 1, \"ant-input-number-handler\", \"ant-input-number-handler-down\", 3, \"mousedown\"], [\"nzType\", \"up\", 1, \"ant-input-number-handler-up-inner\"], [\"nzType\", \"down\", 1, \"ant-input-number-handler-down-inner\"]],\n    template: function NzInputNumberComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵtemplate(0, NzInputNumberComponent_Conditional_0_Template, 1, 1, null, 8)(1, NzInputNumberComponent_Conditional_1_Template, 1, 1, null, 8)(2, NzInputNumberComponent_Conditional_2_Template, 1, 1, null, 8)(3, NzInputNumberComponent_ng_template_3_Template, 5, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, NzInputNumberComponent_ng_template_5_Template, 2, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(7, NzInputNumberComponent_ng_template_7_Template, 3, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(9, NzInputNumberComponent_ng_template_9_Template, 3, 3, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(11, NzInputNumberComponent_ng_template_11_Template, 4, 10, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.hasAddon() ? 0 : ctx.hasAffix() ? 1 : 2);\n      }\n    },\n    dependencies: [NzIconModule, i2.NzIconDirective, NzFormItemFeedbackIconComponent, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputNumberComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-input-number',\n      exportAs: 'nzInputNumber',\n      imports: [NzIconModule, NzFormItemFeedbackIconComponent, NgTemplateOutlet],\n      template: `\n    @if (hasAddon()) {\n      <ng-template [ngTemplateOutlet]=\"inputNumberWithAddonInner\" />\n    } @else if (hasAffix()) {\n      <ng-template [ngTemplateOutlet]=\"inputNumberWithAffixInner\" />\n    } @else {\n      <ng-template [ngTemplateOutlet]=\"inputNumberInner\" />\n    }\n\n    <ng-template #inputNumberWithAddonInner>\n      <div class=\"ant-input-number-wrapper ant-input-number-group\">\n        @if (addonBefore()) {\n          <div class=\"ant-input-number-group-addon\">\n            <ng-content select=\"[nzInputAddonBefore]\"></ng-content>\n          </div>\n        }\n\n        @if (hasAffix()) {\n          <ng-template [ngTemplateOutlet]=\"inputNumberWithAffix\" />\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"inputNumber\" />\n        }\n\n        @if (addonAfter()) {\n          <div class=\"ant-input-number-group-addon\">\n            <ng-content select=\"[nzInputAddonAfter]\"></ng-content>\n          </div>\n        }\n      </div>\n    </ng-template>\n\n    <ng-template #inputNumberWithAffix>\n      <div [class]=\"affixWrapperClass()\">\n        <ng-template [ngTemplateOutlet]=\"inputNumberWithAffixInner\" />\n      </div>\n    </ng-template>\n\n    <ng-template #inputNumberWithAffixInner>\n      @if (prefix()) {\n        <span class=\"ant-input-number-prefix\">\n          <ng-content select=\"[nzInputPrefix]\"></ng-content>\n        </span>\n      }\n      <ng-template [ngTemplateOutlet]=\"inputNumber\" />\n      @if (suffix() || hasFeedback()) {\n        <span class=\"ant-input-number-suffix\">\n          <ng-content select=\"[nzInputSuffix]\"></ng-content>\n          @if (hasFeedback() && finalStatus()) {\n            <nz-form-item-feedback-icon [status]=\"finalStatus()\" />\n          }\n        </span>\n      }\n    </ng-template>\n\n    <ng-template #inputNumber>\n      <div #inputNumberHost [class]=\"inputNumberClass()\">\n        <ng-template [ngTemplateOutlet]=\"inputNumberInner\" />\n      </div>\n    </ng-template>\n\n    <ng-template #inputNumberInner>\n      @if (nzControls()) {\n        <div #handlers class=\"ant-input-number-handler-wrap\" (mouseup)=\"stopAutoStep()\" (mouseleave)=\"stopAutoStep()\">\n          <span\n            role=\"button\"\n            unselectable=\"on\"\n            class=\"ant-input-number-handler ant-input-number-handler-up\"\n            [class.ant-input-number-handler-up-disabled]=\"upDisabled()\"\n            [attr.aria-disabled]=\"upDisabled()\"\n            (mousedown)=\"onStepMouseDown($event, true)\"\n          >\n            <ng-content select=\"[nzInputNumberUpIcon]\">\n              <nz-icon nzType=\"up\" class=\"ant-input-number-handler-up-inner\" />\n            </ng-content>\n          </span>\n          <span\n            role=\"button\"\n            unselectable=\"on\"\n            class=\"ant-input-number-handler ant-input-number-handler-down\"\n            [class.ant-input-number-handler-down-disabled]=\"downDisabled()\"\n            [attr.aria-disabled]=\"downDisabled()\"\n            (mousedown)=\"onStepMouseDown($event, false)\"\n          >\n            <ng-content select=\"[nzInputNumberDownIcon]\">\n              <nz-icon nzType=\"down\" class=\"ant-input-number-handler-down-inner\" />\n            </ng-content>\n          </span>\n        </div>\n      }\n\n      <div class=\"ant-input-number-input-wrap\">\n        <input\n          #input\n          autocomplete=\"off\"\n          role=\"spinbutton\"\n          class=\"ant-input-number-input\"\n          [attr.aria-valuemin]=\"nzMin()\"\n          [attr.aria-valuemax]=\"nzMax()\"\n          [attr.id]=\"nzId()\"\n          [attr.step]=\"nzStep()\"\n          [attr.value]=\"displayValue()\"\n          [value]=\"displayValue()\"\n          [placeholder]=\"nzPlaceHolder() ?? ''\"\n          [disabled]=\"finalDisabled()\"\n          [readOnly]=\"nzReadOnly()\"\n          (input)=\"onInput(input.value)\"\n        />\n      </div>\n    </ng-template>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzInputNumberComponent),\n        multi: true\n      }, {\n        provide: NZ_SPACE_COMPACT_ITEM_TYPE,\n        useValue: 'input-number'\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class]': 'class()',\n        '(keydown)': 'onKeyDown($event)'\n      },\n      hostDirectives: [NzSpaceCompactItemDirective]\n    }]\n  }], () => [], null);\n})();\n/**\n * When click and hold on a button - the speed of auto changing the value.\n */\nconst STEP_INTERVAL = 200;\n/**\n * When click and hold on a button - the delay before auto changing the value.\n */\nconst STEP_DELAY = 600;\nfunction defaultParser(value) {\n  return +value.trim().replace(/。/g, '.');\n}\nfunction isInRange(value, min, max) {\n  return value >= min && value <= max;\n}\n/**\n * if max > 0, round down with precision. Example: input= 3.5, max= 3.5, precision=0; output= 3\n * if max < 0, round up   with precision. Example: input=-3.5, max=-3.5, precision=0; output=-4\n * if min > 0, round up   with precision. Example: input= 3.5, min= 3.5, precision=0; output= 4\n * if min < 0, round down with precision. Example: input=-3.5, min=-3.5, precision=0; output=-3\n */\nfunction getRangeValue(value, min, max, precision = null) {\n  if (precision === null) {\n    if (value < min) {\n      return min;\n    }\n    if (value > max) {\n      return max;\n    }\n    return value;\n  }\n  const fixedValue = +value.toFixed(precision);\n  const multiple = Math.pow(10, precision);\n  if (fixedValue < min) {\n    return Math.ceil(min * multiple) / multiple;\n  }\n  if (fixedValue > max) {\n    return Math.floor(max * multiple) / multiple;\n  }\n  return fixedValue;\n}\nfunction getDecimalPlaces(num) {\n  return num.toString().split('.')[1]?.length || 0;\n}\nfunction isNotCompleteNumber(value) {\n  return /[.。](\\d*0)?$/.test(value.toString());\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputNumberModule {\n  static ɵfac = function NzInputNumberModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzInputNumberModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzInputNumberModule,\n    imports: [NzInputNumberComponent, NzInputAddonBeforeDirective, NzInputAddonAfterDirective, NzInputPrefixDirective, NzInputSuffixDirective],\n    exports: [NzInputNumberComponent, NzInputAddonBeforeDirective, NzInputAddonAfterDirective, NzInputPrefixDirective, NzInputSuffixDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzInputNumberComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputNumberModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzInputNumberComponent, NzInputAddonBeforeDirective, NzInputAddonAfterDirective, NzInputPrefixDirective, NzInputSuffixDirective],\n      exports: [NzInputNumberComponent, NzInputAddonBeforeDirective, NzInputAddonAfterDirective, NzInputPrefixDirective, NzInputSuffixDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzInputNumberComponent, NzInputNumberModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,iBAAiB;AAC9B,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;AAChN,IAAM,MAAM,CAAC,wBAAwB,uBAAuB,mBAAmB,mBAAmB,yBAAyB,yBAAyB;AACpJ,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AAAA,EACtG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,+BAAkC,YAAY,CAAC;AACrD,IAAG,WAAW,oBAAoB,4BAA4B;AAAA,EAChE;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AAAA,EACtG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,+BAAkC,YAAY,CAAC;AACrD,IAAG,WAAW,oBAAoB,4BAA4B;AAAA,EAChE;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AAAA,EACtG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,sBAAyB,YAAY,EAAE;AAC7C,IAAG,WAAW,oBAAoB,mBAAmB;AAAA,EACvD;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAAC;AAC7F,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,eAAe,CAAC;AAAA,EACpH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAAC;AAC7F,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,eAAe,CAAC;AAAA,EACpH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,iBAAoB,YAAY,EAAE;AACxC,IAAG,WAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,OAAO,EAAE;AAC5U,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,IAAI,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,SAAS,IAAI,IAAI,CAAC;AAC1C,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,WAAW,IAAI,IAAI,EAAE;AAAA,EAC/C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,+BAAkC,YAAY,CAAC;AACrD,IAAG,WAAW,OAAO,kBAAkB,CAAC;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,4BAA4B;AAAA,EAChE;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,8BAA8B,EAAE;AAAA,EAClD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,YAAY,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,8BAA8B,EAAE;AAClI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,YAAY,KAAK,OAAO,YAAY,IAAI,IAAI,EAAE;AAAA,EACxE;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC1Q;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,EAAE;AACxC,IAAG,cAAc,OAAO,OAAO,IAAI,IAAI,EAAE;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc;AAChD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,OAAO,KAAK,OAAO,YAAY,IAAI,IAAI,EAAE;AAAA,EACnE;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,sBAAyB,YAAY,EAAE;AAC7C,IAAG,WAAW,OAAO,iBAAiB,CAAC;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,mBAAmB;AAAA,EACvD;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,WAAW,SAAS,sFAAsF;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC,EAAE,cAAc,SAAS,yFAAyF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,aAAa,SAAS,uFAAuF,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,aAAa,GAAG,GAAG,MAAM,mFAAmF,GAAG,CAAC;AACnH,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,aAAa,SAAS,uFAAuF,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,KAAK,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,aAAa,GAAG,GAAG,MAAM,mFAAmF,GAAG,CAAC;AACnH,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,wCAAwC,OAAO,WAAW,CAAC;AAC1E,IAAG,YAAY,iBAAiB,OAAO,WAAW,CAAC;AACnD,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,0CAA0C,OAAO,aAAa,CAAC;AAC9E,IAAG,YAAY,iBAAiB,OAAO,aAAa,CAAC;AAAA,EACvD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,OAAO,EAAE;AAC9F,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC;AACjD,IAAG,WAAW,SAAS,SAAS,wEAAwE;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,YAAY,CAAC;AACjC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,SAAS,KAAK,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,WAAW,IAAI,IAAI,EAAE;AAC7C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,OAAO,aAAa,CAAC,EAAE,gBAAgB,UAAU,OAAO,cAAc,OAAO,QAAQ,YAAY,SAAY,UAAU,EAAE,EAAE,YAAY,OAAO,cAAc,CAAC,EAAE,YAAY,OAAO,WAAW,CAAC;AACrN,IAAG,YAAY,iBAAiB,OAAO,MAAM,CAAC,EAAE,iBAAiB,OAAO,MAAM,CAAC,EAAE,MAAM,OAAO,KAAK,CAAC,EAAE,QAAQ,OAAO,OAAO,CAAC,EAAE,SAAS,OAAO,aAAa,CAAC;AAAA,EAC/J;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,MAAM,IAAI;AAAA,EACjB,SAAS,MAAM,SAAS;AAAA,EACxB,gBAAgB,MAAM,IAAI;AAAA,EAC1B,WAAW,MAAM,EAAE;AAAA,EACnB,SAAS,MAAM,GAAG;AAAA,IAChB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,QAAQ,MAAM,OAAO,kBAAkB;AAAA,IACrC,WAAW;AAAA,EACb,CAAC;AAAA,EACD,QAAQ,MAAM,OAAO,kBAAkB;AAAA,IACrC,WAAW;AAAA,EACb,CAAC;AAAA,EACD,cAAc,MAAM,IAAI;AAAA,EACxB,WAAW,MAAM;AAAA,EACjB,cAAc,MAAM;AAAA,EACpB,aAAa,MAAM,OAAO;AAAA,IACxB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,aAAa,MAAM,OAAO;AAAA,IACxB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,cAAc,MAAM,OAAO;AAAA,IACzB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,aAAa,MAAM,MAAM;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,aAAa,MAAM,MAAM;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,aAAa,MAAM,MAAM;AAAA,IACvB,WAAW;AAAA,EACb,CAAC;AAAA,EACD,SAAS,OAAO;AAAA,EAChB,UAAU,OAAO;AAAA,EACjB,WAAW,OAAO;AAAA,EAClB,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,wBAAwB;AAAA,EACxB,cAAc,OAAO,uBAAuB;AAAA,IAC1C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,UAAU,SAAS,OAAO;AAAA,EACrC,UAAU,UAAU,iBAAiB;AAAA,EACrC,aAAa,OAAO,UAAU;AAAA,EAC9B,WAAW,OAAO,QAAQ;AAAA,EAC1B,eAAe,OAAO,YAAY;AAAA,EAClC,iBAAiB,OAAO,cAAc;AAAA,EACtC,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB;AAAA,EAChB,kBAAkB,WAAS;AACzB,UAAM,YAAY,KAAK,YAAY;AACnC,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,MAAM,QAAQ,SAAS;AAAA,IAChC;AACA,WAAO,MAAM,SAAS;AAAA,EACxB;AAAA,EACA,QAAQ,OAAO,IAAI;AAAA,EACnB,eAAe,OAAO,EAAE;AAAA,EACxB,MAAM,SAAS,KAAK,eAAe,QAAQ;AAAA,IACzC,cAAc,KAAK,eAAe;AAAA,EACpC,CAAC;AAAA,EACD,UAAU,OAAO,KAAK;AAAA,EACtB,cAAc,OAAO,KAAK;AAAA,EAC1B,cAAc,aAAa,MAAM,KAAK,SAAS,CAAC;AAAA,EAChD,gBAAgB,aAAa,MAAM,KAAK,WAAW,CAAC;AAAA,EACpD,SAAS,aAAa,sBAAsB;AAAA,EAC5C,SAAS,aAAa,sBAAsB;AAAA,EAC5C,cAAc,aAAa,2BAA2B;AAAA,EACtD,aAAa,aAAa,0BAA0B;AAAA,EACpD,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,KAAK,YAAY,CAAC;AAAA,EAClF,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,KAAK,WAAW,CAAC;AAAA,EACrE,QAAQ,SAAS,MAAM;AACrB,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,KAAK,kBAAkB;AAAA,IAChC;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,KAAK,kBAAkB;AAAA,IAChC;AACA,WAAO,KAAK,iBAAiB;AAAA,EAC/B,CAAC;AAAA,EACD,mBAAmB,SAAS,MAAM;AAChC,WAAO;AAAA,MACL,oBAAoB;AAAA,MACpB,uBAAuB,KAAK,UAAU,MAAM;AAAA,MAC5C,uBAAuB,KAAK,UAAU,MAAM;AAAA,MAC5C,6BAA6B,KAAK,cAAc;AAAA,MAChD,6BAA6B,KAAK,WAAW;AAAA,MAC7C,+BAA+B,CAAC,KAAK,WAAW;AAAA,MAChD,4BAA4B,KAAK,QAAQ;AAAA,MACzC,wBAAwB,KAAK,IAAI,MAAM;AAAA,MACvC,iCAAiC,CAAC,CAAC,KAAK;AAAA,MACxC,iCAAiC,KAAK,MAAM,MAAM,QAAQ,CAAC,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AAAA,OAC1G,oBAAoB,oBAAoB,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC;AAAA,EAErF,CAAC;AAAA,EACD,oBAAoB,SAAS,MAAM;AACjC,WAAO;AAAA,MACL,kCAAkC;AAAA,MAClC,2CAA2C,KAAK,cAAc;AAAA,MAC9D,2CAA2C,KAAK,WAAW;AAAA,MAC3D,6CAA6C,CAAC,KAAK,WAAW;AAAA,MAC9D,0CAA0C,KAAK,QAAQ;AAAA,MACvD,sCAAsC,KAAK,IAAI,MAAM;AAAA,OAClD,oBAAoB,kCAAkC,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC;AAAA,EAEnG,CAAC;AAAA,EACD,oBAAoB,SAAS,MAAM;AACjC,WAAO;AAAA,MACL,kCAAkC;AAAA,MAClC,sCAAsC,KAAK,IAAI,MAAM;AAAA,OAClD,oBAAoB,kCAAkC,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC;AAAA,EAEnG,CAAC;AAAA,EACD,YAAY,SAAS,MAAM;AACzB,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AAAA,EACD,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,EAC5D,CAAC;AAAA,EACD,eAAe,SAAS,MAAM;AAC5B,WAAO,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,EAC5D,CAAC;AAAA,EACD,cAAc;AACZ,UAAM,aAAa,OAAO,UAAU;AACpC,oBAAgB,MAAM;AACpB,YAAM,UAAU,KAAK,QAAQ;AAC7B,YAAM,UAAU,UAAU,UAAU,KAAK;AACzC,WAAK,aAAa,QAAQ,SAAS,IAAI,EAAE,KAAK,mBAAmB,UAAU,CAAC,EAAE,UAAU,YAAU;AAChG,aAAK,QAAQ,IAAI,CAAC,CAAC,MAAM;AACzB,YAAI,QAAQ;AACV,eAAK,QAAQ,KAAK;AAAA,QACpB,OAAO;AACL,eAAK,SAAS;AACd,eAAK,UAAU;AACf,eAAK,OAAO,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AACD,iBAAW,UAAU,MAAM;AACzB,aAAK,aAAa,eAAe,OAAO;AAAA,MAC1C,CAAC;AAAA,IACH,CAAC;AACD,SAAK,qBAAqB,kBAAkB,KAAK,mBAAmB,CAAC,EAAE,UAAU,CAAC;AAAA,MAChF;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,YAAY,IAAI,MAAM;AAC3B,WAAK,YAAY,IAAI,WAAW;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,QAAI,KAAK,YAAY,GAAG;AACtB,sBAAgB,MAAM,KAAK,MAAM,GAAG;AAAA,QAClC,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,cAAU,MAAM;AACd,WAAK,MAAM,IAAI,KAAK;AACpB,WAAK,SAAS,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,cAAU,MAAM;AACd,WAAK,cAAc,IAAI,KAAK,yBAAyB,KAAK,WAAW,KAAK,QAAQ;AAAA,IACpF,CAAC;AACD,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,EAAE,cAAc,MAAM;AAAA,EACtC;AAAA,EACA,OAAO;AACL,SAAK,SAAS,EAAE,cAAc,KAAK;AAAA,EACrC;AAAA,EACA,KAAK,OAAO,IAAI;AAEd,QAAI,MAAM,KAAK,WAAW,KAAK,CAAC,MAAM,KAAK,aAAa,GAAG;AACzD;AAAA,IACF;AAEA,QAAI,OAAO,MAAM,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO;AAC7D,QAAI,CAAC,IAAI;AACP,aAAO,CAAC;AAAA,IACV;AACA,UAAM,SAAS,iBAAiB,IAAI;AACpC,UAAM,WAAW,MAAM;AACvB,UAAM,YAAY;AAAA;AAAA,OAEjB,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK,QAAQ,IAAI,KAAK,MAAM,OAAO,QAAQ,KAAK;AAAA,MAAU,KAAK,MAAM;AAAA,MAAG,KAAK,MAAM;AAAA,MAAG,KAAK,YAAY;AAAA,IAAC;AACrI,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,KAAK;AAAA,MACjB,MAAM,KAAK,OAAO;AAAA,MAClB,OAAO,KAAK,MAAM;AAAA,MAClB,QAAQ,KAAK,OAAO;AAAA,IACtB,CAAC;AACD,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS,OAAO;AACd,UAAM,YAAY,KAAK,YAAY,KAAK,KAAK;AAC7C,UAAM,YAAY,KAAK,YAAY;AACnC,QAAI,SAAS,SAAS,GAAG;AACvB,gBAAU,CAAC,MAAM,QAAQ,SAAS;AAAA,IACpC;AACA,UAAM,gBAAgB,UAAU,OAAO,KAAK,UAAU,KAAK;AAC3D,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,UAAU,IAAI;AAChB,WAAK,aAAa,IAAI,EAAE;AACxB,WAAK,YAAY,IAAI;AACrB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,SAAS,KAAK;AAClC,UAAM,cAAc,OAAO,KAAK;AAChC,QAAI,oBAAoB,KAAK,KAAK,OAAO,MAAM,WAAW,GAAG;AAC3D,WAAK,aAAa,IAAI,KAAK;AAC3B;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,SAAS;AACjF,SAAK,aAAa,IAAI,cAAc;AACpC,QAAI,CAAC,UAAU,aAAa,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,GAAG;AACvD;AAAA,IACF;AACA,SAAK,YAAY,WAAW;AAAA,EAC9B;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,MAAM,MAAM,OAAO;AAC1B,WAAK,MAAM,IAAI,KAAK;AACpB,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,UAAM,eAAe,KAAK,aAAa;AACvC,QAAI,iBAAiB,IAAI;AACvB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,SAAS,KAAK;AAClC,QAAI,aAAa,OAAO,YAAY;AAEpC,QAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,mBAAa,KAAK,MAAM;AAAA,IAC1B,OAAO;AACL,YAAM,YAAY,KAAK,YAAY;AAEnC,UAAI,SAAS,SAAS,KAAK,iBAAiB,UAAU,MAAM,WAAW;AACrE,qBAAa,CAAC,WAAW,QAAQ,SAAS;AAAA,MAC5C;AAEA,UAAI,CAAC,UAAU,YAAY,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,GAAG;AACtD,qBAAa,cAAc,YAAY,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,SAAS;AAAA,MAC9E;AAAA,IACF;AACA,SAAK,SAAS,UAAU;AAAA,EAC1B;AAAA,EACA,eAAe;AACb,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,aAAa;AAC/B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO,IAAI;AACzB,UAAM,eAAe;AACrB,SAAK,aAAa;AAClB,SAAK,KAAK,OAAO,EAAE;AAEnB,UAAM,WAAW,MAAM;AACrB,WAAK,KAAK,OAAO,EAAE;AACnB,WAAK,gBAAgB,WAAW,UAAU,aAAa;AAAA,IACzD;AAEA,SAAK,gBAAgB,WAAW,UAAU,UAAU;AAAA,EACtD;AAAA,EACA,UAAU,OAAO;AACf,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,cAAM,eAAe;AACrB,aAAK,WAAW,KAAK,KAAK,KAAK,OAAO,IAAI;AAC1C;AAAA,MACF,KAAK;AACH,cAAM,eAAe;AACrB,aAAK,WAAW,KAAK,KAAK,KAAK,OAAO,KAAK;AAC3C;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,IACJ;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,gBAAgB,SAAS,sCAAsC,IAAI,KAAK,UAAU;AAChF,UAAI,KAAK,GAAG;AACV,QAAG,qBAAqB,UAAU,IAAI,QAAQ,wBAAwB,CAAC;AACvE,QAAG,qBAAqB,UAAU,IAAI,QAAQ,wBAAwB,CAAC;AACvE,QAAG,qBAAqB,UAAU,IAAI,aAAa,6BAA6B,CAAC;AACjF,QAAG,qBAAqB,UAAU,IAAI,YAAY,4BAA4B,CAAC;AAAA,MACjF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,IACA,WAAW,SAAS,6BAA6B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,kBAAkB,IAAI,UAAU,KAAK,CAAC;AACzC,QAAG,kBAAkB,IAAI,SAAS,KAAK,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,kDAAkD,QAAQ;AAC1F,iBAAO,IAAI,UAAU,MAAM;AAAA,QAC7B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,MAAM,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,YAAY,CAAC,GAAG,YAAY;AAAA,IAC9B;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,uBAAsB;AAAA,MACpD,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,GAAM,wBAAwB,CAAI,2BAA2B,CAAC,CAAC;AAAA,IACjE,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,wBAAwB,EAAE,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,4BAA4B,wBAAwB,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,gBAAgB,OAAO,QAAQ,cAAc,GAAG,0BAA0B,GAAG,SAAS,SAAS,eAAe,YAAY,UAAU,GAAG,CAAC,GAAG,iCAAiC,GAAG,WAAW,YAAY,GAAG,CAAC,QAAQ,UAAU,gBAAgB,MAAM,GAAG,4BAA4B,+BAA+B,GAAG,WAAW,GAAG,CAAC,QAAQ,UAAU,gBAAgB,MAAM,GAAG,4BAA4B,iCAAiC,GAAG,WAAW,GAAG,CAAC,UAAU,MAAM,GAAG,mCAAmC,GAAG,CAAC,UAAU,QAAQ,GAAG,qCAAqC,CAAC;AAAA,IAC1hC,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+CAA+C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+CAA+C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+CAA+C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,gDAAgD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC3uB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,iBAAiB,iCAAiC,gBAAgB;AAAA,IAClG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,cAAc,iCAAiC,gBAAgB;AAAA,MACzE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8GV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,sBAAsB;AAAA,QACpD,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,gBAAgB,CAAC,2BAA2B;AAAA,IAC9C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAIH,IAAM,gBAAgB;AAItB,IAAM,aAAa;AACnB,SAAS,cAAc,OAAO;AAC5B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,MAAM,GAAG;AACxC;AACA,SAAS,UAAU,OAAO,KAAK,KAAK;AAClC,SAAO,SAAS,OAAO,SAAS;AAClC;AAOA,SAAS,cAAc,OAAO,KAAK,KAAK,YAAY,MAAM;AACxD,MAAI,cAAc,MAAM;AACtB,QAAI,QAAQ,KAAK;AACf,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK;AACf,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,MAAM,QAAQ,SAAS;AAC3C,QAAM,WAAW,KAAK,IAAI,IAAI,SAAS;AACvC,MAAI,aAAa,KAAK;AACpB,WAAO,KAAK,KAAK,MAAM,QAAQ,IAAI;AAAA,EACrC;AACA,MAAI,aAAa,KAAK;AACpB,WAAO,KAAK,MAAM,MAAM,QAAQ,IAAI;AAAA,EACtC;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU;AACjD;AACA,SAAS,oBAAoB,OAAO;AAClC,SAAO,eAAe,KAAK,MAAM,SAAS,CAAC;AAC7C;AAMA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,wBAAwB,6BAA6B,4BAA4B,wBAAwB,sBAAsB;AAAA,IACzI,SAAS,CAAC,wBAAwB,6BAA6B,4BAA4B,wBAAwB,sBAAsB;AAAA,EAC3I,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,sBAAsB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,wBAAwB,6BAA6B,4BAA4B,wBAAwB,sBAAsB;AAAA,MACzI,SAAS,CAAC,wBAAwB,6BAA6B,4BAA4B,wBAAwB,sBAAsB;AAAA,IAC3I,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}