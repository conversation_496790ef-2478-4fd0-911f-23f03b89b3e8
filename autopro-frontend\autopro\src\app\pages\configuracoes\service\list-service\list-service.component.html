<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle"
    >
</app-titulo>

<!-- NOVO -->
<div class="page-header">
    <app-cad-service 
        [isVisible]="showModalNovo" 
        [model]="serviceModel"
        (modalClose)="onModalClosed()">
    </app-cad-service> 
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo</button>
    </div>
 </div> 

 <div class="page-content">
 <nz-table  #basicTable [nzLoading]="isLoadingTable" ngSkipHydration="true" [nzData]="lstService" [nzBordered]="true" [nzSize]="'middle'">
    <thead>
      <tr>
        <th>ID</th>
        <th>Nome</th>
        <th>Ícone</th>
        <th>Descrição</th>
        <th>Valor (R$)</th>
        <th>Ativo</th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of basicTable.data">
        <td>{{ item.id }}</td>
        <td>{{ item.name }}</td>
        <td>
          <div class="icon-display">
            <!-- Exibir imagem se for um arquivo de imagem -->
            <img *ngIf="isImageFile(item.icon)"
                 [src]="getImagePath(item.icon)"
                 [alt]="item.name"
                 style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;">

            <!-- Exibir ícone FontAwesome se não for uma imagem -->
            <div *ngIf="!isImageFile(item.icon)" class="fa-icon-display">
              <i class="fa {{ item.icon }}" style="font-size: 16px; color: #1890ff;"></i>
              <span style="margin-left: 8px;">{{ item.icon }}</span>
            </div>

            <!-- Exibir nome do arquivo se for uma imagem -->
            <span *ngIf="isImageFile(item.icon)" style="margin-left: 8px; font-size: 12px; color: #666;">
              {{ item.icon }}
            </span>
          </div>
        </td>
        <td>{{ item.description }}</td>
        <td>R$ {{ item.valueAmount | number:'1.2-2' }}</td>
        <td>
          <nz-tag [nzColor]="item.active ? 'green' : 'red'">
            {{ item.active ? 'Sim' : 'Não' }}
          </nz-tag>
        </td>
        <td>
            <button nz-button nzTooltipTitle="Editar" nzType="text" nz-tooltip="Editar" (click)="onEdit(item)">
                <i nz-icon nzType="edit" nzTheme="outline"></i>
              </button>
              <a
      nz-popconfirm
      nzPopconfirmTitle="Deseja excluir esse registro?"
      nzOkText="ok"
      nzCancelText="Cancelar"
      (nzOnConfirm)="onDelete(item.id)"
      (nzOnCancel)="cancel()"
    >
      <button nz-button nzTooltipTitle="Excluir" nzType="text" nz-tooltip="Excluir">
        <i nz-icon nzType="delete" nzTheme="outline"></i>
      </button>
    </a>
        </td>
      </tr>
    </tbody>
  </nz-table>
 </div>
