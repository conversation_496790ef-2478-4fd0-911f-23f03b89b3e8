import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from './config.service';
import { Observable } from 'rxjs';
import { UserGroupModel } from '../app/pages/configuracoes/user-group/model/usergroup.model';

@Injectable({
  providedIn: 'root'
})
export class GlobalService {

  url : string = 'http://localhost:5090/api/';
  environment: string = 'dev';
  
  constructor(
    private http: HttpClient,
    private config: ConfigService
  ) 
  { 
    this.url = this.getEnvironmentUrl();
  }

  post(endpoint: string, body: any) {
     return this.http
                .post(this.url + endpoint, body, {headers: this.config.getHeaders()});
  }

  postFile(endpoint: string, formData: FormData) {
    // Para upload de arquivos, não incluímos o Content-Type header
    // O browser define automaticamente como multipart/form-data
    const headers = {
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    };
    return this.http.post(this.url + endpoint, formData, { headers });
  }

  get(endpoint: string) {
    debugger;
    return this.http
              .get(this.url + endpoint, {headers: this.config.getHeaders()});
  }
  
  getbyid(endpoint: string, id: number) {
    return this.http
               .get(`${this.url}${endpoint}/${id}`, {headers: this.config.getHeaders()});
  }
  put(endpoint: string, body: any) {
    debugger;
    return this.http
                .put(this.url + endpoint, body, {headers: this.config.getHeaders()});
  }

  delete(endpoint: string) {
    debugger;
    let url = `${this.url}${endpoint}`;
    return this.http
                .delete(url, {headers: this.config.getHeaders()});
  }

  getEnvironmentUrl() {
    if (this.environment == 'dev') 
      return 'http://localhost:5090/api/';
    else if (this.environment == 'qa') 
      return 'http://localhost:5090/api/';
    else if (this.environment == 'prod') 
      return 'http://localhost:5090/api/';  
    else 
      return 'http://localhost:5090/api/';
  }
}
