﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\service-icons\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png'))">
      <SourceType>Package</SourceType>
      <SourceId>AutoVPro</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AutoVPro</BasePath>
      <RelativePath>uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vpvb6w4lrr</Fingerprint>
      <Integrity>UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\service-icons\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>