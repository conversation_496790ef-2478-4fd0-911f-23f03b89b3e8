import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  MenuService,
  NzIsMenuInsideDropDownToken,
  NzMenuModule
} from "./chunk-HFOHYYSY.js";
import {
  NzButtonGroupComponent
} from "./chunk-74XN5Q3N.js";
import {
  POSITION_MAP
} from "./chunk-Q7NBCKF4.js";
import {
  ConnectionPositionPair,
  Overlay,
  TemplatePortal
} from "./chunk-ZMKSW6B3.js";
import {
  NzNoAnimationDirective
} from "./chunk-63L2WPNR.js";
import {
  slideMotion
} from "./chunk-SRMRLS6L.js";
import {
  ESCAPE,
  hasModifierKey
} from "./chunk-4D7LIHDQ.js";
import {
  Platform
} from "./chunk-REGSU76D.js";
import {
  __esDecorate,
  __runInitializers
} from "./chunk-B2WI67Z2.js";
import {
  NzConfigService,
  WithConfig
} from "./chunk-KXPJCL5I.js";
import {
  fromEventOutsideAngular
} from "./chunk-LMUE7R35.js";
import {
  Directionality
} from "./chunk-C36UWHOI.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Directive,
  ElementRef,
  EventEmitter,
  Injectable,
  Input,
  NgModule,
  NgZone,
  Output,
  Renderer2,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation,
  booleanAttribute,
  inject,
  require_operators,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleMap,
  ɵɵtemplate,
  ɵɵviewQuery
} from "./chunk-SBDEEQKR.js";
import {
  require_cjs
} from "./chunk-AQYIT73X.js";
import {
  __toESM
} from "./chunk-YHCV7DAQ.js";

// node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-dropdown.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var _c0 = ["*"];
function NzDropdownMenuComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 0);
    ɵɵlistener("@slideMotion.done", function NzDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onAnimationEvent($event));
    })("mouseenter", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.setMouseState(true));
    })("mouseleave", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.setMouseState(false));
    });
    ɵɵprojection(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵstyleMap(ctx_r1.nzOverlayStyle);
    ɵɵclassMap(ctx_r1.nzOverlayClassName);
    ɵɵclassProp("ant-dropdown-rtl", ctx_r1.dir === "rtl");
    ɵɵproperty("@slideMotion", void 0)("@.disabled", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))("nzNoAnimation", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);
  }
}
var NZ_CONFIG_MODULE_NAME = "dropDown";
var listOfPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];
var NzDropDownDirective = (() => {
  let _nzBackdrop_decorators;
  let _nzBackdrop_initializers = [];
  let _nzBackdrop_extraInitializers = [];
  return class NzDropDownDirective2 {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? /* @__PURE__ */ Object.create(null) : void 0;
      _nzBackdrop_decorators = [WithConfig()];
      __esDecorate(null, null, _nzBackdrop_decorators, {
        kind: "field",
        name: "nzBackdrop",
        static: false,
        private: false,
        access: {
          has: (obj) => "nzBackdrop" in obj,
          get: (obj) => obj.nzBackdrop,
          set: (obj, value) => {
            obj.nzBackdrop = value;
          }
        },
        metadata: _metadata
      }, _nzBackdrop_initializers, _nzBackdrop_extraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, {
        enumerable: true,
        configurable: true,
        writable: true,
        value: _metadata
      });
    }
    nzConfigService;
    renderer;
    viewContainerRef;
    platform;
    _nzModuleName = NZ_CONFIG_MODULE_NAME;
    elementRef = inject(ElementRef);
    overlay = inject(Overlay);
    portal;
    overlayRef = null;
    destroy$ = new import_rxjs.Subject();
    positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn(".ant-dropdown");
    inputVisible$ = new import_rxjs.BehaviorSubject(false);
    nzTrigger$ = new import_rxjs.BehaviorSubject("hover");
    overlayClose$ = new import_rxjs.Subject();
    nzDropdownMenu = null;
    nzTrigger = "hover";
    nzMatchWidthElement = null;
    nzBackdrop = __runInitializers(this, _nzBackdrop_initializers, false);
    nzClickHide = (__runInitializers(this, _nzBackdrop_extraInitializers), true);
    nzDisabled = false;
    nzVisible = false;
    nzOverlayClassName = "";
    nzOverlayStyle = {};
    nzPlacement = "bottomLeft";
    nzVisibleChange = new EventEmitter();
    setDropdownMenuValue(key, value) {
      if (this.nzDropdownMenu) {
        this.nzDropdownMenu.setValue(key, value);
      }
    }
    constructor(nzConfigService, renderer, viewContainerRef, platform) {
      this.nzConfigService = nzConfigService;
      this.renderer = renderer;
      this.viewContainerRef = viewContainerRef;
      this.platform = platform;
    }
    ngAfterViewInit() {
      if (this.nzDropdownMenu) {
        const nativeElement = this.elementRef.nativeElement;
        const hostMouseState$ = (0, import_rxjs.merge)((0, import_rxjs.fromEvent)(nativeElement, "mouseenter").pipe((0, import_operators.map)(() => true)), (0, import_rxjs.fromEvent)(nativeElement, "mouseleave").pipe((0, import_operators.map)(() => false)));
        const menuMouseState$ = this.nzDropdownMenu.mouseState$;
        const mergedMouseState$ = (0, import_rxjs.merge)(menuMouseState$, hostMouseState$);
        const hostClickState$ = (0, import_rxjs.fromEvent)(nativeElement, "click").pipe((0, import_operators.map)(() => !this.nzVisible));
        const visibleStateByTrigger$ = this.nzTrigger$.pipe((0, import_operators.switchMap)((trigger) => {
          if (trigger === "hover") {
            return mergedMouseState$;
          } else if (trigger === "click") {
            return hostClickState$;
          } else {
            return import_rxjs.EMPTY;
          }
        }));
        const descendantMenuItemClick$ = this.nzDropdownMenu.descendantMenuItemClick$.pipe((0, import_operators.filter)(() => this.nzClickHide), (0, import_operators.map)(() => false));
        const domTriggerVisible$ = (0, import_rxjs.merge)(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe((0, import_operators.filter)(() => !this.nzDisabled));
        const visible$ = (0, import_rxjs.merge)(this.inputVisible$, domTriggerVisible$);
        (0, import_rxjs.combineLatest)([visible$, this.nzDropdownMenu.isChildSubMenuOpen$]).pipe((0, import_operators.map)(([visible, sub]) => visible || sub), (0, import_operators.auditTime)(150), (0, import_operators.distinctUntilChanged)(), (0, import_operators.filter)(() => this.platform.isBrowser), (0, import_operators.takeUntil)(this.destroy$)).subscribe((visible) => {
          const element = this.nzMatchWidthElement ? this.nzMatchWidthElement.nativeElement : nativeElement;
          const triggerWidth = element.getBoundingClientRect().width;
          if (this.nzVisible !== visible) {
            this.nzVisibleChange.emit(visible);
          }
          this.nzVisible = visible;
          if (visible) {
            if (!this.overlayRef) {
              this.overlayRef = this.overlay.create({
                positionStrategy: this.positionStrategy,
                minWidth: triggerWidth,
                disposeOnNavigation: true,
                hasBackdrop: this.nzBackdrop && this.nzTrigger === "click",
                scrollStrategy: this.overlay.scrollStrategies.reposition()
              });
              (0, import_rxjs.merge)(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe((0, import_operators.filter)((e) => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe((0, import_operators.filter)((e) => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe(() => {
                this.overlayClose$.next(false);
              });
            } else {
              const overlayConfig = this.overlayRef.getConfig();
              overlayConfig.minWidth = triggerWidth;
            }
            this.positionStrategy.withPositions([POSITION_MAP[this.nzPlacement], ...listOfPositions]);
            if (!this.portal || this.portal.templateRef !== this.nzDropdownMenu.templateRef) {
              this.portal = new TemplatePortal(this.nzDropdownMenu.templateRef, this.viewContainerRef);
            }
            this.overlayRef.attach(this.portal);
          } else {
            if (this.overlayRef) {
              this.overlayRef.detach();
            }
          }
        });
        this.nzDropdownMenu.animationStateChange$.pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe((event) => {
          if (event.toState === "void") {
            if (this.overlayRef) {
              this.overlayRef.dispose();
            }
            this.overlayRef = null;
          }
        });
      }
    }
    ngOnDestroy() {
      this.destroy$.next(true);
      this.destroy$.complete();
      if (this.overlayRef) {
        this.overlayRef.dispose();
        this.overlayRef = null;
      }
    }
    ngOnChanges(changes) {
      const {
        nzVisible,
        nzDisabled,
        nzOverlayClassName,
        nzOverlayStyle,
        nzTrigger
      } = changes;
      if (nzTrigger) {
        this.nzTrigger$.next(this.nzTrigger);
      }
      if (nzVisible) {
        this.inputVisible$.next(this.nzVisible);
      }
      if (nzDisabled) {
        const nativeElement = this.elementRef.nativeElement;
        if (this.nzDisabled) {
          this.renderer.setAttribute(nativeElement, "disabled", "");
          this.inputVisible$.next(false);
        } else {
          this.renderer.removeAttribute(nativeElement, "disabled");
        }
      }
      if (nzOverlayClassName) {
        this.setDropdownMenuValue("nzOverlayClassName", this.nzOverlayClassName);
      }
      if (nzOverlayStyle) {
        this.setDropdownMenuValue("nzOverlayStyle", this.nzOverlayStyle);
      }
    }
    static ɵfac = function NzDropDownDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || NzDropDownDirective2)(ɵɵdirectiveInject(NzConfigService), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Platform));
    };
    static ɵdir = ɵɵdefineDirective({
      type: NzDropDownDirective2,
      selectors: [["", "nz-dropdown", ""]],
      hostAttrs: [1, "ant-dropdown-trigger"],
      inputs: {
        nzDropdownMenu: "nzDropdownMenu",
        nzTrigger: "nzTrigger",
        nzMatchWidthElement: "nzMatchWidthElement",
        nzBackdrop: [2, "nzBackdrop", "nzBackdrop", booleanAttribute],
        nzClickHide: [2, "nzClickHide", "nzClickHide", booleanAttribute],
        nzDisabled: [2, "nzDisabled", "nzDisabled", booleanAttribute],
        nzVisible: [2, "nzVisible", "nzVisible", booleanAttribute],
        nzOverlayClassName: "nzOverlayClassName",
        nzOverlayStyle: "nzOverlayStyle",
        nzPlacement: "nzPlacement"
      },
      outputs: {
        nzVisibleChange: "nzVisibleChange"
      },
      exportAs: ["nzDropdown"],
      features: [ɵɵNgOnChangesFeature]
    });
  };
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzDropDownDirective, [{
    type: Directive,
    args: [{
      selector: "[nz-dropdown]",
      exportAs: "nzDropdown",
      host: {
        class: "ant-dropdown-trigger"
      }
    }]
  }], () => [{
    type: NzConfigService
  }, {
    type: Renderer2
  }, {
    type: ViewContainerRef
  }, {
    type: Platform
  }], {
    nzDropdownMenu: [{
      type: Input
    }],
    nzTrigger: [{
      type: Input
    }],
    nzMatchWidthElement: [{
      type: Input
    }],
    nzBackdrop: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    nzClickHide: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    nzDisabled: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    nzVisible: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    nzOverlayClassName: [{
      type: Input
    }],
    nzOverlayStyle: [{
      type: Input
    }],
    nzPlacement: [{
      type: Input
    }],
    nzVisibleChange: [{
      type: Output
    }]
  });
})();
var NzContextMenuServiceModule = class _NzContextMenuServiceModule {
  static ɵfac = function NzContextMenuServiceModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzContextMenuServiceModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _NzContextMenuServiceModule
  });
  static ɵinj = ɵɵdefineInjector({});
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzContextMenuServiceModule, [{
    type: NgModule
  }], null, null);
})();
var NzDropDownADirective = class _NzDropDownADirective {
  static ɵfac = function NzDropDownADirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzDropDownADirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _NzDropDownADirective,
    selectors: [["a", "nz-dropdown", ""]],
    hostAttrs: [1, "ant-dropdown-link"]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzDropDownADirective, [{
    type: Directive,
    args: [{
      selector: "a[nz-dropdown]",
      host: {
        class: "ant-dropdown-link"
      }
    }]
  }], null, null);
})();
var NzDropdownButtonDirective = class _NzDropdownButtonDirective {
  renderer;
  elementRef;
  nzButtonGroupComponent = inject(NzButtonGroupComponent, {
    host: true,
    optional: true
  });
  constructor(renderer, elementRef) {
    this.renderer = renderer;
    this.elementRef = elementRef;
  }
  ngAfterViewInit() {
    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);
    if (this.nzButtonGroupComponent && parentElement) {
      this.renderer.addClass(parentElement, "ant-dropdown-button");
    }
  }
  static ɵfac = function NzDropdownButtonDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzDropdownButtonDirective)(ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ElementRef));
  };
  static ɵdir = ɵɵdefineDirective({
    type: _NzDropdownButtonDirective,
    selectors: [["", "nz-button", "", "nz-dropdown", ""]]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzDropdownButtonDirective, [{
    type: Directive,
    args: [{
      selector: "[nz-button][nz-dropdown]"
    }]
  }], () => [{
    type: Renderer2
  }, {
    type: ElementRef
  }], null);
})();
var NzDropdownMenuComponent = class _NzDropdownMenuComponent {
  cdr;
  elementRef;
  renderer;
  viewContainerRef;
  directionality;
  mouseState$ = new import_rxjs.BehaviorSubject(false);
  nzMenuService = inject(MenuService);
  isChildSubMenuOpen$ = this.nzMenuService.isChildSubMenuOpen$;
  descendantMenuItemClick$ = this.nzMenuService.descendantMenuItemClick$;
  animationStateChange$ = new EventEmitter();
  nzOverlayClassName = "";
  nzOverlayStyle = {};
  templateRef;
  dir = "ltr";
  destroy$ = new import_rxjs.Subject();
  onAnimationEvent(event) {
    this.animationStateChange$.emit(event);
  }
  setMouseState(visible) {
    this.mouseState$.next(visible);
  }
  setValue(key, value) {
    this[key] = value;
    this.cdr.markForCheck();
  }
  noAnimation = inject(NzNoAnimationDirective, {
    host: true,
    optional: true
  });
  constructor(cdr, elementRef, renderer, viewContainerRef, directionality) {
    this.cdr = cdr;
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.viewContainerRef = viewContainerRef;
    this.directionality = directionality;
  }
  ngOnInit() {
    this.directionality.change?.pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.cdr.detectChanges();
    });
    this.dir = this.directionality.value;
  }
  ngAfterContentInit() {
    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static ɵfac = function NzDropdownMenuComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzDropdownMenuComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Directionality));
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _NzDropdownMenuComponent,
    selectors: [["nz-dropdown-menu"]],
    viewQuery: function NzDropdownMenuComponent_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(TemplateRef, 7);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.templateRef = _t.first);
      }
    },
    exportAs: ["nzDropdownMenu"],
    features: [ɵɵProvidersFeature([
      MenuService,
      /** menu is inside dropdown-menu component **/
      {
        provide: NzIsMenuInsideDropDownToken,
        useValue: true
      }
    ])],
    ngContentSelectors: _c0,
    decls: 1,
    vars: 0,
    consts: [[1, "ant-dropdown", 3, "mouseenter", "mouseleave", "nzNoAnimation"]],
    template: function NzDropdownMenuComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵprojectionDef();
        ɵɵtemplate(0, NzDropdownMenuComponent_ng_template_0_Template, 2, 9, "ng-template");
      }
    },
    dependencies: [NzNoAnimationDirective],
    encapsulation: 2,
    data: {
      animation: [slideMotion]
    },
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzDropdownMenuComponent, [{
    type: Component,
    args: [{
      selector: `nz-dropdown-menu`,
      exportAs: `nzDropdownMenu`,
      animations: [slideMotion],
      providers: [
        MenuService,
        /** menu is inside dropdown-menu component **/
        {
          provide: NzIsMenuInsideDropDownToken,
          useValue: true
        }
      ],
      template: `
    <ng-template>
      <div
        class="ant-dropdown"
        [class.ant-dropdown-rtl]="dir === 'rtl'"
        [class]="nzOverlayClassName"
        [style]="nzOverlayStyle"
        @slideMotion
        (@slideMotion.done)="onAnimationEvent($event)"
        [@.disabled]="!!noAnimation?.nzNoAnimation"
        [nzNoAnimation]="noAnimation?.nzNoAnimation"
        (mouseenter)="setMouseState(true)"
        (mouseleave)="setMouseState(false)"
      >
        <ng-content></ng-content>
      </div>
    </ng-template>
  `,
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      imports: [NzNoAnimationDirective]
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: ViewContainerRef
  }, {
    type: Directionality
  }], {
    templateRef: [{
      type: ViewChild,
      args: [TemplateRef, {
        static: true
      }]
    }]
  });
})();
var NzDropDownModule = class _NzDropDownModule {
  static ɵfac = function NzDropDownModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzDropDownModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _NzDropDownModule,
    imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],
    exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]
  });
  static ɵinj = ɵɵdefineInjector({
    imports: [NzContextMenuServiceModule, NzMenuModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzDropDownModule, [{
    type: NgModule,
    args: [{
      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],
      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]
    }]
  }], null, null);
})();
var LIST_OF_POSITIONS = [new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "start",
  overlayY: "top"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "start",
  overlayY: "bottom"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "end",
  overlayY: "bottom"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "end",
  overlayY: "top"
})];
var NzContextMenuService = class _NzContextMenuService {
  ngZone;
  overlay;
  overlayRef = null;
  closeSubscription = import_rxjs.Subscription.EMPTY;
  constructor(ngZone, overlay) {
    this.ngZone = ngZone;
    this.overlay = overlay;
  }
  create($event, nzDropdownMenuComponent) {
    this.close(true);
    const {
      x,
      y
    } = $event;
    if ($event instanceof MouseEvent) {
      $event.preventDefault();
    }
    const positionStrategy = this.overlay.position().flexibleConnectedTo({
      x,
      y
    }).withPositions(LIST_OF_POSITIONS).withTransformOriginOn(".ant-dropdown");
    this.overlayRef = this.overlay.create({
      positionStrategy,
      disposeOnNavigation: true,
      scrollStrategy: this.overlay.scrollStrategies.close()
    });
    this.closeSubscription = new import_rxjs.Subscription();
    this.closeSubscription.add(nzDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));
    this.closeSubscription.add((0, import_rxjs.merge)(fromEventOutsideAngular(document, "click").pipe(
      (0, import_operators.filter)((event) => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)),
      /** handle firefox contextmenu event **/
      (0, import_operators.filter)((event) => event.button !== 2)
    ), fromEventOutsideAngular(document, "keydown").pipe((0, import_operators.filter)((event) => event.key === "Escape"))).pipe((0, import_operators.first)()).subscribe(() => this.ngZone.run(() => this.close())));
    return this.overlayRef.attach(new TemplatePortal(nzDropdownMenuComponent.templateRef, nzDropdownMenuComponent.viewContainerRef));
  }
  close(clear = false) {
    if (this.overlayRef) {
      this.overlayRef.detach();
      if (clear) {
        this.overlayRef.dispose();
      }
      this.overlayRef = null;
      this.closeSubscription.unsubscribe();
    }
  }
  static ɵfac = function NzContextMenuService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NzContextMenuService)(ɵɵinject(NgZone), ɵɵinject(Overlay));
  };
  static ɵprov = ɵɵdefineInjectable({
    token: _NzContextMenuService,
    factory: _NzContextMenuService.ɵfac,
    providedIn: NzContextMenuServiceModule
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzContextMenuService, [{
    type: Injectable,
    args: [{
      providedIn: NzContextMenuServiceModule
    }]
  }], () => [{
    type: NgZone
  }, {
    type: Overlay
  }], null);
})();

export {
  NzDropDownDirective,
  NzContextMenuServiceModule,
  NzDropDownADirective,
  NzDropdownButtonDirective,
  NzDropdownMenuComponent,
  NzDropDownModule,
  NzContextMenuService
};
//# sourceMappingURL=chunk-TQ2JKWKF.js.map
