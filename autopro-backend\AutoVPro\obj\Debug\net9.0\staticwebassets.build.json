{"Version": 1, "Hash": "qk8mzjDSxOqEocxHYiM+yv+kAMWWKQN095HXFXTDyVQ=", "Source": "AutoVPro", "BasePath": "_content/AutoVPro", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AutoVPro\\wwwroot", "Source": "AutoVPro", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Projetos\\Freelas\\Thiago Dantas\\Autopro\\autopro-backend\\AutoVPro\\wwwroot\\", "BasePath": "_content/AutoVPro", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\Projetos\\Freelas\\<PERSON>hia<PERSON>\\Autopro\\autopro-backend\\AutoVPro\\wwwroot\\uploads\\service-icons\\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "SourceId": "AutoVPro", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Projetos\\Freelas\\Thiago Dantas\\Autopro\\autopro-backend\\AutoVPro\\wwwroot\\", "BasePath": "_content/AutoVPro", "RelativePath": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vpvb6w4lrr", "Integrity": "UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\service-icons\\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png"}], "Endpoints": [{"Route": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\Projetos\\Freelas\\<PERSON>hia<PERSON>\\Autopro\\autopro-backend\\AutoVPro\\wwwroot\\uploads\\service-icons\\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "183882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:02:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="}]}, {"Route": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.vpvb6w4lrr.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\Projetos\\Freelas\\<PERSON>hia<PERSON>\\Autopro\\autopro-backend\\AutoVPro\\wwwroot\\uploads\\service-icons\\ef7bd165-3a4b-486c-bf9e-87a4b717a014.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "183882"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:02:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vpvb6w4lrr"}, {"Name": "label", "Value": "uploads/service-icons/ef7bd165-3a4b-486c-bf9e-87a4b717a014.png"}, {"Name": "integrity", "Value": "sha256-UzsVREf5aNwmOcqdBWrISPZjx1iG8LEOQyZZvRWjMK8="}]}]}