using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/upload")]
    public class UploadController : ControllerBase
    {
        private readonly IWebHostEnvironment _environment;
        
        public UploadController(IWebHostEnvironment environment)
        {
            _environment = environment;
        }
        
        [HttpPost]
        [Route("service-icon")]
        public async Task<IActionResult> UploadServiceIcon(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("Nenhum arquivo foi enviado.");
                }

                // Validar tipo de arquivo
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/svg+xml" };
                if (!allowedTypes.Contains(file.ContentType))
                {
                    return BadRequest("Tipo de arquivo não permitido. Use apenas JPG, PNG ou SVG.");
                }

                // Validar tamanho do arquivo (máximo 2MB)
                const int maxSize = 2 * 1024 * 1024; // 2MB
                if (file.Length > maxSize)
                {
                    return BadRequest("Arquivo muito grande. O tamanho máximo é 2MB.");
                }

                // Criar diretório se não existir
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "service-icons");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                // Gerar nome único para o arquivo
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // Salvar arquivo
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Retornar informações do arquivo
                return Ok(new
                {
                    fileName = fileName,
                    originalName = file.FileName,
                    size = file.Length,
                    url = $"/uploads/service-icons/{fileName}"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpDelete]
        [Route("service-icon/{fileName}")]
        public IActionResult DeleteServiceIcon(string fileName)
        {
            try
            {
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "service-icons");
                var filePath = Path.Combine(uploadsPath, fileName);

                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                    return Ok(new { message = "Arquivo deletado com sucesso." });
                }

                return NotFound("Arquivo não encontrado.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
    }
}
